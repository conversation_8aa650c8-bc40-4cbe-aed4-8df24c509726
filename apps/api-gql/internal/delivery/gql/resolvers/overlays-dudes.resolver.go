package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.57

import (
	"context"
	"fmt"

	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/gqlmodel"
)

// DudesUpdate is the resolver for the dudesUpdate field.
func (r *mutationResolver) DudesUpdate(ctx context.Context, id string, input gqlmodel.DudesOverlaySettingsInput) (bool, error) {
	panic(fmt.Errorf("not implemented: DudesUpdate - dudesUpdate"))
}

// Dudes<PERSON><PERSON> is the resolver for the dudesCreate field.
func (r *mutationResolver) DudesCreate(ctx context.Context, input gqlmodel.DudesOverlaySettingsInput) (bool, error) {
	panic(fmt.Errorf("not implemented: DudesCreate - dudesCreate"))
}

// Dudes<PERSON><PERSON><PERSON> is the resolver for the dudesDelete field.
func (r *mutationResolver) DudesDelete(ctx context.Context, id string) (bool, error) {
	panic(fmt.Errorf("not implemented: DudesDelete - dudesDelete"))
}

// DudesGetByID is the resolver for the dudesGetById field.
func (r *queryResolver) DudesGetByID(ctx context.Context, id string) (*gqlmodel.DudesOverlaySettings, error) {
	panic(fmt.Errorf("not implemented: DudesGetByID - dudesGetById"))
}

// DudesGetAll is the resolver for the dudesGetAll field.
func (r *queryResolver) DudesGetAll(ctx context.Context) ([]gqlmodel.DudesOverlaySettings, error) {
	panic(fmt.Errorf("not implemented: DudesGetAll - dudesGetAll"))
}
