package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.57

import (
	"context"
	"fmt"

	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/gqlmodel"
)

// IntegrationsDonateStreamPostSecret is the resolver for the integrationsDonateStreamPostSecret field.
func (r *mutationResolver) IntegrationsDonateStreamPostSecret(ctx context.Context, input gqlmodel.DonateStreamPostSecret) (bool, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return false, err
	}

	err = r.deps.DonateStreamIntegrationService.PostCode(ctx, dashboardID, input.Secret)
	if err != nil {
		return false, fmt.Errorf("cannot post donate stream secret: %w", err)
	}

	return true, nil
}

// IntegrationsDonateStream is the resolver for the integrationsDonateStream field.
func (r *queryResolver) IntegrationsDonateStream(ctx context.Context) (*gqlmodel.DonateStreamResponse, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return nil, err
	}

	id, err := r.deps.DonateStreamIntegrationService.GetIDByChannelID(ctx, dashboardID)
	if err != nil {
		return nil, err
	}

	if id == nil {
		return nil, fmt.Errorf("donate stream: internal error")
	}

	return &gqlmodel.DonateStreamResponse{
		IntegrationID: *id,
	}, nil
}
